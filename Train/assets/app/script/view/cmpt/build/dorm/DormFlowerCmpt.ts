import { BUILD_MOUNT_POINT, BuildFromType } from "../../../../common/constant/Enums";
import StateObj from "../../../../model/passenger/StateObj";
import DormFlowerObj, { DormFlowerObjDieAnim, DormFlowerObjGrowAnim, DormFlowerObjIdleAnim, DormFlowerObjState } from "../../../../model/train/dorm/DormFlowerObj";
import DormLeftBedObj, { DormLeftBedObjState } from "../../../../model/train/dorm/DormLeftBedObj";
import TrainCarriage from "../../../train/TrainCarriage";
import MountPointCmpt from "../../common/MountPointCmpt";
import BuildCmpt from "../BuildCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class DormFlowerCmpt extends BuildCmpt {

    public model: DormFlowerObj = null
    private preState: StateObj<DormFlowerObjState> = null

    public init(model: DormFlowerObj, carriageCmpt: TrainCarriage, from: BuildFromType = BuildFromType.NONE) {
        this.model = model
        this.carriageCmpt = carriageCmpt
    }

    protected updateBState(sk: sp.Skeleton) {
        let state = this.model.state
        if (this.preState == state) return
        this.preState = state
        let type = state?.type
        let data = state?.data

        let ani = ""
        let loop = false

        switch (type) {
            case DormFlowerObjState.GROW:
                ani = DormFlowerObjGrowAnim[this.model.objType]
                loop = false
                break
            case DormFlowerObjState.IDLE:
                ani = DormFlowerObjIdleAnim[this.model.objType]
                loop = true
                break
            case DormFlowerObjState.DIE:
                ani = DormFlowerObjDieAnim[this.model.objType]
                loop = false
                break
            default:
                sk.node.parent.opacity = 0
        }

        if (ani) {
            sk.node.parent.opacity = 255
            sk.playAnimation(ani, loop, data?.timeData?.elapsed)
        }
    }

}
