import { MAX_ZINDEX } from "../../../../common/constant/Constant";
import { BUILD_MOUNT_POINT, RoleDir } from "../../../../common/constant/Enums";
import { gameHelper } from "../../../../common/helper/GameHelper";
import { StateType } from "../../../../model/passenger/StateEnum";
import StateObj from "../../../../model/passenger/StateObj";
import BuildObj from "../../../../model/train/common/BuildObj";
import DormBirdModel from "../../../../model/train/dorm/DormBirdModel";
import MountPointCmpt from "../../common/MountPointCmpt";
import BuildCmpt from "../BuildCmpt";

const { ccclass, property } = cc._decorator;

enum BirdAnim {
    BIAO_YAN_1 = "biaoyan_1",
    BIAO_YAN_2 = "biaoyan_2",
    DAI_JI_1 = "daiji_1",
    DAI_JI_2 = "daiji_2",
    FEI_1 = "fei_1",
    FEI_2 = "fei_2",
    FEI_3 = "fei_3",
}

@ccclass
export default class DormBirdView extends mc.BaseCmptCtrl {

    @property(cc.Node)
    body: cc.Node = null

    private model: DormBirdModel = null
    private prePos: cc.Vec2 = null
    private state: StateObj<StateType> = null
    private orgParent: cc.Node = null

    get sk() { return this.body.Component(sp.Skeleton) }

    public init(model: DormBirdModel) {
        this.model = model
        this.initView()
    }

    private initView() {
        const sk = this.sk
        sk.setSkin(this.model.skin)
        this.updatePosition()
    }

    private updatePosition() {
        let pos = this.getModelPos()
        this.node.setPosition(pos)
        this.updateZIndex()
        this.updateDir()
        if (this.prePos) {
            this.prePos.set(this.node.getPosition())
        }
    }

    private updateZIndex() {
        this.node.zIndex = MAX_ZINDEX - this.model.position.y
    }

    private getModelPos() { return this.model.getPosition() }

    private updateDir() {
        if (!this.prePos) return;
        let _dir = this.model.dir
        if (this.model.isMoving) {
            this.setDir(_dir)
            return
        }
        let vec = this.getModelPos().sub(this.prePos)
        if (vec.x != 0) {
            let dir = vec.x < 0 ? RoleDir.LEFT : RoleDir.RIGHT
            this.setDir(dir)
        }
    }

    public setDir(dir: RoleDir) {
        let curScale = this.body.scale
        let scale = dir == RoleDir.LEFT ? -Math.abs(curScale) : Math.abs(curScale)
        if (curScale != scale) {
            this.body.scaleX = scale
        }
    }


    update(dt: number) {
        if (!this.model) return
        this.model.update(dt)

        if (!this.prePos) this.prePos = this.getModelPos().clone()

        if (!this.orgParent) this.orgParent = this.node.parent

        this.updateState()
    }

    private updateState() {
        let state = this.model.state
        if (this.state == state) return
        this.state = state
        if (!state) {
            return void this.playIdle()
        }
        // this.updatePosition()
        let type = state?.type
        let data = state?.data

        switch (true) {
            case type == StateType.BIRD_SLEEP:
                this.playSleep()
                break
            case type == StateType.BIRD_BIAO_YAN_1:
                console.warn("biaoyan 1 state")
                break
            case type == StateType.BIRD_BIAO_YAN_2:
                console.warn("biaoyan 2 state")
                break
            case type == StateType.BIRD_FLY:
                this.playFly2(data)
                break
            case type == StateType.BIRD_IDLE:
                this.playIdle()
                break
        }
    }

    private async playFly2(data: { toBuild: BuildObj, fromBuild: BuildObj, resolve: Function, pos: cc.Vec2 }) {
        const build = data.toBuild
        const pos = data.pos
        const sk = this.sk
        let targetPos: cc.Vec2 = null
        let pointNode: cc.Node = null
        if (build) {
            const buildNode = this.getBuildNode(build.id)
            pointNode = buildNode.Component(MountPointCmpt).getPoint(BUILD_MOUNT_POINT.BIRD)

            if (pointNode.childrenCount > 0) {
                const randomNode = pointNode.children.filter(it => !it.childrenCount && !it["_lock_"]).random()
                pointNode = randomNode || pointNode
            }
            pointNode["_lock_"] = true
            targetPos = ut.convertToNodeAR(pointNode, this.orgParent)
        }
        else {
            targetPos = pos
        }
        this.exitBuild(data.fromBuild)

        const distance = this.node.getPosition().sub(targetPos).mag()
        const time = distance / 500
        this.model.isMoving = true

        await sk.playAnimation(BirdAnim.FEI_1, false)
        sk.playAnimation(BirdAnim.FEI_2, true)
        const startPos = this.node.getPosition()
        const endPos = targetPos
        // 飞行方向和距离
        const deltaX = endPos.x - startPos.x
        const deltaY = endPos.y - startPos.y
        const horizontalDistance = Math.abs(deltaX)
        // 根据飞行方向 模拟真实鸟类飞行
        let controlY: number
        if (deltaY > 50) {
            // 向上飞行：需要更大的弧度来克服重力
            controlY = startPos.y + horizontalDistance * 0.4 + deltaY * 0.3
        } else if (deltaY < -50) {
            // 向下飞行：先稍微上升然后下降，模拟自然滑翔
            controlY = Math.max(startPos.y, endPos.y) + horizontalDistance * 0.2
        } else {
            // 水平飞行：轻微的波浪形轨迹
            controlY = Math.max(startPos.y, endPos.y) + horizontalDistance * 0.15
        }
        const controlX = startPos.x + deltaX * 0.5
        const controlPoint = cc.v2(controlX, controlY)
        await cc.tween(this.node).bezierTo(time, controlPoint, controlPoint, endPos).promise()

        this.model.setPosition(targetPos)
        this.updateZIndex()
        if (pointNode) {
            this.node.parent = pointNode
            this.node.setPosition(0, 0)
        }
        await sk.playAnimation(BirdAnim.FEI_3, false)
        this.model.isMoving = false
        data.resolve()
    }

    protected exitBuild(build: BuildObj) {
        this.node.zIndex = MAX_ZINDEX
        if (this.node.parent == this.orgParent) return
        if (build) {
            build.setUseLock(false, 0, this.model.skin)
        }
        const pointNode = this.node.parent
        pointNode["_lock_"] = false
        let pos = ut.convertToNodeAR(this.node.parent, this.orgParent)
        this.node.setParent(this.orgParent)
        this.node.setPosition(pos)
    }

    protected async playIdle() {
        this.sk.playAnimation(BirdAnim.DAI_JI_1, true)
    }

    protected async playSleep() {
        this.sk.playAnimation(BirdAnim.DAI_JI_2, true)
    }


    public getBuildNode(id: string): cc.Node {
        const root = this.orgParent
        for (let child of root.children) {
            const cmpt = child.getComponent(BuildCmpt)
            if (!cmpt) continue
            if (!cmpt.isActive()) continue
            if (!cmpt.model) continue
            if (cmpt.model.id == id) {
                return child
            }
        }
        console.error("??")
        return null
    }

}
