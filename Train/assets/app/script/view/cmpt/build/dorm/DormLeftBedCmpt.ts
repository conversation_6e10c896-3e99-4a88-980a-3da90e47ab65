import { BUILD_MOUNT_POINT } from "../../../../common/constant/Enums";
import DormLeftBedObj, { DormLeftBedObjState } from "../../../../model/train/dorm/DormLeftBedObj";
import MountPointCmpt from "../../common/MountPointCmpt";
import BuildCmpt from "../BuildCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class DormLeftBedCmpt extends BuildCmpt {

    public model: DormLeftBedObj = null

    protected updateBState(sk: sp.Skeleton) {
        let state = this.model.state
        let type = state?.type
        let data = state?.data

        switch (type) {
            case DormLeftBedObjState.SWING:
                sk.playAnimation("ani_swing", false, data.timeData.elapsed)
                break;
            case DormLeftBedObjState.SING_ENTER:
                sk.playAnimation("sing_enter", false, data.timeData.elapsed)
                break;
            case DormLeftBedObjState.SING_IDLE:
                sk.playAnimation("sing_idle", false, data.timeData.elapsed)
                break;
            case DormLeftBedObjState.SING_EXIT:
                sk.playAnimation("sing_exit", false, data.timeData.elapsed)
                break;
            default:
                sk.playAnimation("jingzhi")
                break;
        }
    }

    public playOnJumpEnter() {
        let sk = this.Child("body", sp.Skeleton)
        sk.playAnimation("aniSit")
    }

    public playOnSit(pointName: string) {
        if (pointName != BUILD_MOUNT_POINT.SLEEP) return
        let body2Node = this.Component(MountPointCmpt).getPoint(BUILD_MOUNT_POINT.BODY2)
        if (body2Node) {
            body2Node.active = false
        }
    }
}
