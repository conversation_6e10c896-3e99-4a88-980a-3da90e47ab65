import { TrainTechCfg } from "../../common/constant/DataType";
import EventType from "../../common/event/EventType";
import { gameHelper } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { viewHelper } from "../../common/helper/ViewHelper";

const { ccclass } = cc._decorator;

@ccclass
export default class TrainTechWdtCtrl extends mc.BaseWdtCtrl {

    //@autocode property begin
    protected lineNode_: cc.Node = null // path://root/line_n
    protected treeSv_: cc.ScrollView = null // path://root/tree_sv
    //@end

    _reversed: boolean = false
    _selectedBtn: cc.Node = null

    public listenEventMaps() {
        return [
            { [EventType.TRAIN_TECH_UPGRADE]: this.onTechUpgrade }
        ]
    }

    public async onCreate() {
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    onTechUpgrade(id: string) {
        this.testLoadJson()
    }

    // ----------------------------------------- custom function ----------------------------------------------------

    public init() {
        this.testLoadJson()
    }

    async testLoadJson() {
        if (this._reversed) {
            this.treeSv_.content.children.reverse()
        }
        const ary = assetsMgr.getJson<TrainTechCfg>("TrainTech").datas
        const dataMap = {}
        const preAry = []
        for (const tech of ary) {
            const pre = tech.pre || []
            const row = tech.row
            const ceil = tech.ceil
            dataMap[row] = dataMap[row] || { ceil: [] }
            dataMap[row].ceil.push(ceil)
            preAry.push(...pre.map(s => { return { cur: tech.tmpId, pre: s } }))
        }
        const tmp = [1, 2, 3, 4, 5]
        this.treeSv_.Items(Object.keys(dataMap), (lineNode: cc.Node, row: string) => {
            lineNode.name = row

            lineNode.Items(tmp, (it: cc.Node, arg: number) => {
                it.name = arg + ""
                it.active = false
            })
            const ceils = dataMap[row].ceil
            const refAry = []
            for (const ceil of ceils) {
                const tmpId = row + "-" + ceil
                const tech = ary.find(t => t.tmpId == tmpId)
                if (tech.pre?.length) {
                    const ref = tech.pre.find(p => p.split("-")[0] == row)
                    if (ref) {
                        refAry.push(ceil)
                        continue
                    }
                }
                const it = lineNode.Child(ceil)
                it.active = true
                it.Data = tmpId
            }
            const lyt = lineNode.getComponent(cc.Layout)
            lyt.updateLayout()
            lyt.enabled = false
            for (const ceil of refAry) {
                const it = lineNode.Child(ceil)
                const tmpId = row + "-" + ceil
                const tech = ary.find(t => t.tmpId == tmpId)
                const pre = tech.pre.find(p => p.split("-")[0] == row)
                const preCeil = pre.split("-")[1]
                const preNode = lineNode.Child(preCeil)
                const isLeft = tmp.indexOf(Number(preCeil)) > tmp.indexOf(Number(ceil))
                const space = lyt.spacingX + lyt.paddingLeft + preNode.width
                it.x = preNode.x + (isLeft ? -space : space)
                it.setSiblingIndex(preNode.getSiblingIndex() - 1)
                it.active = true
                it.Data = tmpId
            }
            lineNode.children.forEach(it => {
                const id = it.Data as string
                if (!id || !it.active) return
                const cfg = assetsMgr.getJson<TrainTechCfg>("TrainTech").datas.find(t => t.tmpId == id)
                const lv = gameHelper.trainTech.getTechLevel(cfg.id)
                if (lv > 0) {
                    it.Child("lv").active = true
                    it.Child("lv").setLocaleKey("common_guiText_11", lv)
                    it.Child("icon").setGray(false)
                    it.Child("icon").setDark(0)
                    it.Child("lock").active = false
                }
                else if (gameHelper.trainTech.checkPre(cfg.id)) {
                    it.Child("lv").active = false
                    it.Child("lock").active = false
                    it.Child("icon").setGray(false)
                    it.Child("icon").setDark(0.4)
                    it.Child("icon").opacity = 204
                }
                else {
                    it.Child("lv").active = false
                    it.Child("icon").setGray(true)
                    it.Child("icon").setDark(0.4)
                    it.Child("lock").active = true
                }
                it.Child("selected").active = false
                resHelper.loadTmpIcon(`tech/${cfg.icon}`, it.Child("icon", cc.Sprite), this.getTag())
                it.off("click")
                it.on("click", this.onClickHandler)
            })

        })
        const lyt = this.treeSv_.content.getComponent(cc.Layout)
        lyt.updateLayout()
        lyt.enabled = false

        this.treeSv_.content.children.reverse()
        this._reversed = true

        await ut.waitNextFrame(1, this)
        this.testRenderLine(preAry)
    }

    testRenderLine(preAry: { cur: string, pre: string }[]) {
        for (const { cur, pre } of preAry) {
            let [curRow, curCeil] = cur.split("-").map(s => Number(s))
            let [preRow, preCeil] = pre.split("-").map(s => Number(s))
            const curNode = this.treeSv_.content.Child(curRow).Child(curCeil)
            const preNode = this.treeSv_.content.Child(preRow).Child(preCeil)
            const name = `${cur}&${pre}`
            let lineNode = curNode.Child(name)
            if (!lineNode) {
                lineNode = cc.instantiate(this.lineNode_)
            }
            const cfg = assetsMgr.getJson<TrainTechCfg>("TrainTech").datas.find(t => t.tmpId == pre)
            lineNode.active = true
            lineNode.parent = curNode
            lineNode.name = name
            lineNode.Component(cc.MultiFrame).setFrame(gameHelper.trainTech.getTechLevel(cfg.id) > 0)
            const curWorldPos = ut.convertToNodeAR(curNode, this.treeSv_.content)
            const preWorldPos = ut.convertToNodeAR(preNode, this.treeSv_.content)
            lineNode.angle = ut.getAngle(preWorldPos, curWorldPos)
            const dis = ut.calculationDis(curWorldPos, preWorldPos)
            lineNode.width = dis
            lineNode.setSiblingIndex(0)

        }
    }

    onClickHandler(btn: cc.Button) {
        if (cc.isValid(this._selectedBtn)) {
            this._selectedBtn.Child("selected").active = false
        }
        btn.node.Child("selected").active = true
        this._selectedBtn = btn.node
        const cfg = assetsMgr.getJson<TrainTechCfg>("TrainTech").datas.find(t => t.tmpId == btn.node.Data)
        viewHelper.showPnl("train/TrainTechOperationPnl", cfg.id)
    }
}
