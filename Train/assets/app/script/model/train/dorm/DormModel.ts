
import { DormBuildType } from "../../../common/constant/Enums";
import CarriageModel from "../common/CarriageModel";
import Dorm<PERSON><PERSON>erObj from "./DormFlowerObj";
import DormLeftBedObj from "./DormLeftBedObj";
import DormRightBedObj from "./DormRightBedObj";

/**
 * 一号宿舍
 */
export default class DormModel extends CarriageModel {

    public init(data) {
        super.init(data)
        return this
    }

    public getLeftBed() {
        return this.getBuildByOrder(DormBuildType.LEFT_BED) as DormLeftBedObj
    }

    public getRightBed() {
        return this.getBuildByOrder(DormBuildType.RIGHT_BED) as DormRightBedObj
    }

    public getBookcase() {
        return this.getBuildByOrder(DormBuildType.BOOKCASE)
    }

    public getLeftChair() {
        return this.getBuildByOrder(DormBuildType.LEFT_CHAIR)
    }

    public getRightChair() {
        return this.getBuildByOrder(DormBuildType.RIGHT_CHAIR)
    }

    public getTable() {
        return this.getBuildByOrder(DormBuildType.TABLE)
    }

    public getEmptyFlower() {
        const ary = this.flowers || []
        if (!ary.length) return null
        for (const flower of ary) {
            const tmp = (flower as DormFlowerObj)
            if (!tmp.inUse) {
                return tmp
            }
        }
        return null
    }

    public getChairs() {
        return [DormBuildType.LEFT_CHAIR, DormBuildType.RIGHT_CHAIR].map((type) => {
            return this.getBuildByOrder(type)
        }).filter(build => !!build)
    }

    public getBeds() {
        return [this.getLeftBed(), this.getRightBed()]
    }

    public newBuildObj(type: DormBuildType) {
        switch (type) {
            case DormBuildType.RIGHT_BED: return new DormRightBedObj()
            case DormBuildType.LEFT_BED: return new DormLeftBedObj()
            case DormBuildType.FLOWER_EXT: return new DormFlowerObj()
            default:
                return super.newBuildObj(type)
        }
    }
}