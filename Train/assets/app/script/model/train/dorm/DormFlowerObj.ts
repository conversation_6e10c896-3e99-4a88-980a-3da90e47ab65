
import { DormBuildType } from "../../../common/constant/Enums"
import { resHelper } from "../../../common/helper/ResHelper"
import ActionTree, { ActionNode } from "../../passenger/ActionTree"
import PassengerModel from "../../passenger/PassengerModel"
import { TimeStateData } from "../../passenger/StateDataType"
import StateObj from "../../passenger/StateObj"
import BuildObj from "../common/BuildObj"

export enum DormFlowerObjState {
    GROW,
    IDLE,
    DIE,
}

export enum DormFlowerObjType {
    NONE,
    FLOWER,
    LEAF
}

export const DormFlowerObjGrowAnim = {
    [DormFlowerObjType.FLOWER]: "enter_1",
    [DormFlowerObjType.LEAF]: "enter_2",
}

export const DormFlowerObjIdleAnim = {
    [DormFlowerObjType.FLOWER]: "idle_1",
    [DormFlowerObjType.LEAF]: "idle_2",
}

export const DormFlowerObjDieAnim = {
    [DormFlowerObjType.FLOWER]: "out_1",
    [DormFlowerObjType.LEAF]: "out_2",
}

// 左边床
export default class DormFlowerObj extends BuildObj {

    public animTime: { [key: string]: number } = {}
    public inUse: boolean = false

    public objType: DormFlowerObjType = DormFlowerObjType.NONE
    public state: StateObj<DormFlowerObjState> = null

    public init(index: number, carriageId: number, needBuild = false) {
        this.id = 1013 + "-" + DormBuildType.FLOWER_EXT + "-" + index
        this.order = index
        this.carriageId = carriageId
        this.isBuilding = true
        return this
    }

    public toGrow() {
        this.reset()
        // 每次都随机 花朵 or 叶子
        this.randomType()
        return this.actionTree.start(this.grow)
    }

    public toIdle() {
        this.reset()
        return this.actionTree.start(this.idle)
    }

    public toDie() {
        this.reset()
        return this.actionTree.start(this.die)
    }

    private randomType() { this.objType = [DormFlowerObjType.FLOWER, DormFlowerObjType.LEAF].random() }
    public getAnimTime(name: string) { return this.animTime[name] }

    protected async baseHandle(action: ActionNode, anim: string, state: DormFlowerObjState) {
        action.onTerminate = () => this.setState()
        let time = this.getAnimTime(anim)
        let timeData = new TimeStateData().init(time)
        this.setState(state, { timeData })
        await action.wait(timeData)
        action.ok()
    }

    public async grow(action: ActionNode) {
        await this.baseHandle(action, DormFlowerObjGrowAnim[this.objType], DormFlowerObjState.GROW)
    }

    public async idle(action: ActionNode) {
        await this.baseHandle(action, DormFlowerObjIdleAnim[this.objType], DormFlowerObjState.IDLE)
    }

    public async die(action: ActionNode) {
        await this.baseHandle(action, DormFlowerObjDieAnim[this.objType], DormFlowerObjState.DIE)
    }

    private setState(type?: DormFlowerObjState, data?: any) {
        this.state = new StateObj<DormFlowerObjState>().init(type, data)
    }


    public reset() {
        this.actionTree.terminate()
    }

    update(dt) {
        this.actionTree && this.actionTree.update(dt)
    }
}