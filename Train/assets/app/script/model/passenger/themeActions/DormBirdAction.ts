import { BUILD_MOUNT_POINT, PassengerLifeAnimation, SpeedUpType } from "../../../common/constant/Enums"
import { gameHelper } from "../../../common/helper/GameHelper"
import BuildObj from "../../train/common/BuildObj"
import DormBirdModel, { TragetType } from "../../train/dorm/DormBirdModel"
import DormModel from "../../train/dorm/DormModel"
import ActionTree, { ActionNode } from "../ActionTree"
import { TimeStateData } from "../StateDataType"
import { StateType } from "../StateEnum"
import BaseAction from "./BaseAction"

export default class DormBirdAction extends BaseAction {

    protected carriage: DormModel = null
    protected _model: DormBirdModel = null

    public setBy(v: DormBirdModel) {
        this._model = v
        this.actionTree = new ActionTree().init(this)
        this.carriage = v.carriage as DormModel
        this.actionTree.start(this.lifeCycle)
        return this
    }

    update(dt: number) {
        if (!this._model) return
        this.actionTree.update(dt)
    }

    debug(...params) {
        // console.log(`${this._model.skin} :`, ...params)
    }

    protected async onBeforeStart(action: ActionNode) {
        await action.wait(ut.randomRange(0.8, 3.5))
    }

    protected async lifeCycle(action: ActionNode) {
        await action.run(this.start)
        action.ok()
    }

    protected async start(action: ActionNode) {
        await this.onBeforeStart(action)
        let cfgs = [
            { act: this.flyIn, weight: 20 },
            { act: this.toSleep, check: this.checkSleep, weight: 50 },
        ]
        await this.runRandomAct(action, cfgs)
        await action.wait(ut.random(2, 4))
    }

    private getBuild(type: TragetType) {
        switch (type) {
            case TragetType.FLOOR:
                return null
            case TragetType.LEFT_CHAIR:
                return this.carriage.getLeftChair()
            case TragetType.RIGHT_CHAIR:
                return this.carriage.getRightChair()
            case TragetType.RIGHT_BED:
                return this.carriage.getRightBed()
        }
    }

    private async flyIn(action: ActionNode) {
        this.debug('flyIn', this._model.skin)
        // 当前在顶部  就不去杆子上了
        const isPosTop = this._model.position.x < 0

        const origin = [
            { type: TragetType.FLOOR, lock: false, weight: 10 },
            { type: TragetType.LEFT_CHAIR, lock: true, weight: 30 },
            { type: TragetType.RIGHT_CHAIR, lock: true, weight: 30 },
            { type: TragetType.RIGHT_BED, lock: false, weight: 30 }
        ]
        const availableTargets = origin.filter(obj => {
            if (!obj.lock) return true
            if (obj.type == TragetType.RIGHT_BED && isPosTop) return false
            const build = this.getBuild(obj.type)
            return build && this.checkBuildPlay(build)
        })

        const stayTime = ut.random(3, 8)
        let index = 0
        const match = availableTargets.filter(t => !t.lock || t.type != this._model.currentTarget)
        const ridx = gameHelper.randomByWeight(match)
        const target = match[ridx]
        action.onTerminate = () => { }

        // 飞去哪个建筑
        const toBuild = this.getBuild(target.type)
        // 原本在哪个建筑
        let fromBuild = this.getBuild(this._model.currentTarget)
        // 如果原本的建筑不参与lock事件，就不需要在退出建筑的时候执行解锁方法
        const curObj = origin.find(t => t.type == this._model.currentTarget)
        if (curObj && !curObj.lock) {
            fromBuild = null
        }
        // 当前目标类型
        this._model.currentTarget = target.type
        if (toBuild) {
            if (target.lock) toBuild.setUseLock(true, index, this._model.skin)
            await action.run(this.flyToBuild, { toBuild, fromBuild })
        }
        else {
            await action.run(this.flyToAnyWhere)
        }
        await action.wait(stayTime)
        action.onTerminate()
        action.ok()
    }

    private async flyToBuild(action: ActionNode) {
        if (this._model.isMoving) return
        const { toBuild, fromBuild } = action.params
        this.debug('flyToBuild', toBuild.id)

        action.onTerminate = () => this._model.popState(StateType.BIRD_FLY)

        let handle = () => new Promise((resolve) => {
            this._model.pushState(StateType.BIRD_FLY, { toBuild, fromBuild, resolve })
        })
        await action.waitFunc(handle)
        action.onTerminate()
        action.ok()
    }

    private async flyToAnyWhere(action: ActionNode) {
        if (this._model.isMoving) return
        this.debug('flyToAnyWhere')
        const pos = this._model.getRandomFlyPos()

        action.onTerminate = () => this._model.popState(StateType.BIRD_FLY)
        let handle = () => new Promise((resolve) => {
            this._model.pushState(StateType.BIRD_FLY, { pos, resolve })
        })
        await action.waitFunc(handle)
        action.onTerminate()
        action.ok()
    }

    protected isSleepTime() {
        if (!gameHelper.world.isNight()) return false
        const today = gameHelper.world.getDay()
        if (this._model.lastSleepDay == today) return false
        const lastAwakeDay = this._model.lastSleepEndTime ? gameHelper.world.getDay(this._model.lastSleepEndTime) : 0
        return today != lastAwakeDay
    }
    protected getAwakeTime() {
        if (!this.isSleepTime()) return 0

        let now = gameHelper.world.getTime()
        let dayTime = gameHelper.world.getDayTime()
        const birdWakeTime = 5 * ut.Time.Hour
        const randomTimeRange = [-30, 30]
        const randomTime = this.randomCount(randomTimeRange)
        let targetWakeTime = birdWakeTime + randomTime * ut.Time.Minute
        let currentDayTime = dayTime % ut.Time.Day

        // 晚上9点后可以睡觉
        const sleepStartTime = 21 * ut.Time.Hour + this.randomCount(randomTimeRange) * ut.Time.Minute
        if (currentDayTime < targetWakeTime) {
            let sleepDuration = targetWakeTime - currentDayTime
            return now + sleepDuration
        } else if (currentDayTime >= sleepStartTime) {
            let sleepDuration = (ut.Time.Day - currentDayTime) + targetWakeTime
            return now + sleepDuration
        } else {
            return 0
        }
    }

    protected checkSleepEnd(action: ActionNode) {
        let time = gameHelper.world.getTime()
        let state = this._model.getState(StateType.BIRD_SLEEP)
        if (!state || state.data.awakeTime <= time) {
            return action.ok()
        }
    }

    protected async toSleep(action: ActionNode) {
        const toBuild = this.carriage.getRightBed()
        const awakeTime = this.getAwakeTime()
        if (awakeTime <= 0) return action.ok()
        this.debug("bird sleep")
        action.onTerminate = () => this._model.popState(StateType.BIRD_SLEEP)
        this._model.lastSleepDay = gameHelper.world.getDay()
        this._model.lastSleepEndTime = awakeTime

        if (toBuild) {
            await action.run(this.flyToBuild, { toBuild })
        }
        else if (this._model.currentTarget == TragetType.UNKNOW) {
            await action.run(this.flyToAnyWhere)
        }
        this._model.pushState(StateType.BIRD_SLEEP, { toBuild, awakeTime })
        await action.run(this.checkSleepEnd)
        action.onTerminate()
        action.ok()
    }

}